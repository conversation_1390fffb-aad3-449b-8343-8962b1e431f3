{
  // Oxlint configuration for the js/ workspace
  // See .augment/rules/oxlint.md for policy
  "ignorePatterns": [
    "dist/**",
    "node_modules/**",
    "dist-typings/**"
  ],
  "categories": {
    "correctness": "error",
    "suspicious": "error",
    "perf": "error",
    "style": "error",
    "restriction": "error"
  },
  "rules": {
    "sort-imports": "off",
    "sort-keys": "off"
  },
  "overrides": [
    {
      "files": [
        "**/*.test.*",
        "**/__tests__/**"
      ],
      "plugins": [
        "jest"
      ],
      "rules": {
        "jest/no-focused-tests": "error",
        "jest/no-disabled-tests": "warn"
      }
    },
    {
      // TypeScript specific tweaks if needed later
      "files": [
        "**/*.ts",
        "**/*.tsx"
      ],
      "plugins": [
        "typescript"
      ],
      "rules": {}
    }
  ]
}