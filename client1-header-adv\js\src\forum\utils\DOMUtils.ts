/**
 * DOM manipulation utilities - functional approach
 */

/**
 * Create a DOM element with specified attributes
 * @param tagName - HTML tag name
 * @param attributes - Element attributes
 * @param innerHTML - Inner HTML content
 * @returns Created element
 */
export const createElement = (
    tagName: string,
    attributes: Record<string, string> = {},
    innerHTML = ''
): HTMLElement => {
    const element = document.createElement(tagName);

    for (const [key, value] of Object.entries(attributes)) {
        if (key === 'className') {
            element.className = value;
        } else if (key === 'style') {
            element.setAttribute('style', value);
        } else {
            element.setAttribute(key, value);
        }
    }

    if (innerHTML) {
        element.innerHTML = innerHTML;
    }

    return element;
};

/**
 * Safely get element by ID
 * @param id - Element ID
 * @returns Element or null if not found
 */
export const getElementById = (id: string): HTMLElement | null => {
    return document.getElementById(id);
};

/**
 * Safely query selector
 * @param selector - CSS selector
 * @param parent - Parent element (default: document)
 * @returns Element or undefined if not found
 */
export const querySelector = (selector: string, parent: Element | Document = document): Element | undefined => {
    try {
        if (!parent || !selector) {
            return undefined;
        }
        return parent.querySelector(selector) ?? undefined;
    } catch {
        return undefined;
    }
};

/**
 * Safely query all elements
 * @param selector - CSS selector
 * @param parent - Parent element (default: document)
 * @returns NodeList of elements
 */
export const querySelectorAll = (selector: string, parent: Element | Document = document): NodeListOf<Element> => {
    try {
        if (!parent || !selector) {
            return document.querySelectorAll(''); // Return empty NodeList
        }
        return parent.querySelectorAll(selector);
    } catch {
        return document.querySelectorAll(''); // Return empty NodeList
    }
};

/**
 * Add event listener with error handling
 * @param element - Target element
 * @param event - Event type
 * @param handler - Event handler
 */
export const addEventListener = (
    element: Element,
    event: string,
    handler: EventListener
): void => {
    try {
        if (element && event && handler) {
            element.addEventListener(event, handler);
        }
    } catch {
        // Silently handle event listener errors
    }
};

/**
 * Remove event listener with error handling
 * @param element - Target element
 * @param event - Event type
 * @param handler - Event handler
 */
export const removeEventListener = (
    element: Element,
    event: string,
    handler: EventListener
): void => {
    try {
        if (element && event && handler) {
            element.removeEventListener(event, handler);
        }
    } catch {
        // Silently handle event listener removal errors
    }
};

/**
 * Set CSS styles on element
 * @param element - Target element
 * @param styles - Style properties
 */
export const setStyles = (element: HTMLElement, styles: Record<string, string>): void => {
    if (!element || !styles) {
        return;
    }
    for (const [property, value] of Object.entries(styles)) {
        try {
            element.style.setProperty(property, value);
        } catch {
            // Silently handle style setting errors
        }
    }
};

/**
 * Append element to parent with error handling
 * @param parent - Parent element
 * @param child - Child element to append
 */
export const appendChild = (parent: Element, child: Element): void => {
    try {
        if (parent && child) {
            parent.appendChild(child);
        }
    } catch {
        // Silently handle append errors
    }
};

/**
 * Prepend element to parent with error handling
 * @param parent - Parent element
 * @param child - Child element to prepend
 */
export const prependChild = (parent: Element, child: Element): void => {
    try {
        if (parent && child && parent.firstChild) {
            parent.firstChild.before(child);
        }
    } catch {
        // Silently handle prepend errors
    }
};

/**
 * Remove element safely
 * @param element - Element to remove
 */
export const removeElement = (element: Element): void => {
    try {
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    } catch {
        // Silently handle element removal errors
    }
};
