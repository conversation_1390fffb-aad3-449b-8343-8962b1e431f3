{"version": 3, "file": "admin.js", "sources": ["../src/admin/components/DynamicSlideSettingsComponent.tsx", "../src/admin/SettingsGenerator.js", "../src/admin/index.js"], "sourcesContent": ["import app from 'flarum/admin/app';\nimport Component from 'flarum/common/Component';\nimport Button from 'flarum/common/components/Button';\n\ninterface SlideData {\n  id: number;\n  link: string;\n  image: string;\n}\n\ninterface DynamicSlideSettingsComponentAttrs {\n  extensionId: string;\n  maxSlides?: number;\n}\n\n/**\n * Dynamic component for managing advertisement slide settings with add/delete functionality\n */\nexport default class DynamicSlideSettingsComponent extends Component<DynamicSlideSettingsComponentAttrs> {\n  private slides: SlideData[] = [];\n  private loading = false;\n  private nextId = 1;\n\n  oninit(vnode: any) {\n    super.oninit(vnode);\n    this.loadExistingSlides();\n  }\n\n  /**\n   * Load existing slides from settings\n   */\n  private loadExistingSlides() {\n    const { extensionId, maxSlides = 30 } = this.attrs;\n    const slides: SlideData[] = [];\n    \n    // Load existing slides from settings\n    for (let i = 1; i <= maxSlides; i++) {\n      const linkKey = `${extensionId}.Link${i}`;\n      const imageKey = `${extensionId}.Image${i}`;\n      const link = app.data.settings[linkKey] || '';\n      const image = app.data.settings[imageKey] || '';\n      \n      // Only include slides that have at least one field filled\n      if (link || image) {\n        slides.push({\n          id: i,\n          link,\n          image\n        });\n        this.nextId = Math.max(this.nextId, i + 1);\n      }\n    }\n    \n    this.slides = slides;\n    \n    // If no slides exist, add one empty slide to start with\n    if (slides.length === 0) {\n      this.addSlide();\n    }\n  }\n\n  /**\n   * Add a new slide\n   */\n  private addSlide() {\n    const newSlide: SlideData = {\n      id: this.nextId++,\n      link: '',\n      image: ''\n    };\n    \n    this.slides.push(newSlide);\n    m.redraw();\n  }\n\n  /**\n   * Remove a slide\n   */\n  private removeSlide(slideId: number) {\n    const { extensionId } = this.attrs;\n    const slideIndex = this.slides.findIndex(slide => slide.id === slideId);\n    \n    if (slideIndex === -1) return;\n    \n    const slide = this.slides[slideIndex];\n    \n    // Remove from backend\n    this.saveSetting(`${extensionId}.Link${slide.id}`, '');\n    this.saveSetting(`${extensionId}.Image${slide.id}`, '');\n    \n    // Remove from local state\n    this.slides.splice(slideIndex, 1);\n    \n    // Ensure at least one slide exists\n    if (this.slides.length === 0) {\n      this.addSlide();\n    }\n    \n    m.redraw();\n  }\n\n  /**\n   * Update slide data\n   */\n  private updateSlide(slideId: number, field: 'link' | 'image', value: string) {\n    const { extensionId } = this.attrs;\n    const slide = this.slides.find(s => s.id === slideId);\n    \n    if (!slide) return;\n    \n    slide[field] = value;\n    \n    // Save to backend\n    const settingKey = field === 'link' \n      ? `${extensionId}.Link${slide.id}` \n      : `${extensionId}.Image${slide.id}`;\n    \n    this.saveSetting(settingKey, value);\n  }\n\n  /**\n   * Save setting to backend with debouncing\n   */\n  private saveSetting(key: string, value: string) {\n    // Clear existing timeout for this key\n    const timeoutKey = `saveTimeout_${key}`;\n    clearTimeout((this as any)[timeoutKey]);\n    \n    // Set new timeout\n    (this as any)[timeoutKey] = setTimeout(() => {\n      app.data.settings[key] = value;\n      \n      app.request({\n        method: 'POST',\n        url: app.forum.attribute('apiUrl') + '/settings',\n        body: {\n          [key]: value\n        }\n      }).catch(() => {\n        // Handle save error silently for now\n      });\n    }, 500);\n  }\n\n  view() {\n    return m('div.Form-group', [\n      m('label.FormLabel', \n        app.translator.trans('wusong8899-client1.admin.SlideSettings')\n      ),\n      m('div.helpText', \n        app.translator.trans('wusong8899-client1.admin.SlideSettingsHelp')\n      ),\n\n      m('div.DynamicSlideSettings', [\n        // Slides list\n        this.slides.map((slide, index) => this.renderSlide(slide, index)),\n        \n        // Add button\n        m('div.DynamicSlideSettings-addButton', [\n          m(Button, {\n            className: 'Button Button--primary',\n            icon: 'fas fa-plus',\n            onclick: () => this.addSlide()\n          }, app.translator.trans('wusong8899-client1.admin.AddSlide'))\n        ])\n      ])\n    ]);\n  }\n\n  /**\n   * Render a single slide\n   */\n  private renderSlide(slide: SlideData, index: number) {\n    return m('div.DynamicSlideSettings-slide', {\n      key: slide.id\n    }, [\n      m('div.DynamicSlideSettings-slideHeader', [\n        m('h4', app.translator.trans('wusong8899-client1.admin.SlideNumber', { number: index + 1 })),\n        m(Button, {\n          className: 'Button Button--danger',\n          icon: 'fas fa-trash',\n          onclick: () => this.removeSlide(slide.id),\n          disabled: this.slides.length === 1\n        }, app.translator.trans('wusong8899-client1.admin.DeleteSlide'))\n      ]),\n      \n      m('div.DynamicSlideSettings-slideFields', [\n        // Link URL field\n        m('div.Form-group', [\n          m('label.FormLabel', \n            app.translator.trans('wusong8899-client1.admin.SlideLink')\n          ),\n          m('input.FormControl', {\n            type: 'url',\n            placeholder: 'https://example.com',\n            value: slide.link,\n            oninput: (e: Event) => {\n              const target = e.target as HTMLInputElement;\n              this.updateSlide(slide.id, 'link', target.value);\n            }\n          })\n        ]),\n        \n        // Image URL field\n        m('div.Form-group', [\n          m('label.FormLabel', \n            app.translator.trans('wusong8899-client1.admin.SlideImage')\n          ),\n          m('input.FormControl', {\n            type: 'url',\n            placeholder: 'https://example.com/image.jpg',\n            value: slide.image,\n            oninput: (e: Event) => {\n              const target = e.target as HTMLInputElement;\n              this.updateSlide(slide.id, 'image', target.value);\n            }\n          })\n        ])\n      ])\n    ]);\n  }\n}\n", "import app from 'flarum/admin/app';\nimport DynamicSlideSettingsComponent from './components/DynamicSlideSettingsComponent';\n\n/**\n * Settings generator utility for admin interface\n */\nexport class SettingsGenerator {\n    constructor(extensionId) {\n        this.extensionId = extensionId;\n        this.extensionData = app.extensionData.for(extensionId);\n    }\n\n    /**\n     * Register transition time setting\n     */\n    registerTransitionTimeSetting() {\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.TransitionTime`,\n            type: 'number',\n            label: app.translator.trans('wusong8899-client1.admin.TransitionTime'),\n        });\n        return this;\n    }\n\n    /**\n     * Register header icon URL setting\n     */\n    registerHeaderIconUrlSetting() {\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.HeaderIconUrl`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.HeaderIconUrl'),\n            help: app.translator.trans('wusong8899-client1.admin.HeaderIconUrlHelp'),\n        });\n        return this;\n    }\n\n    /**\n     * Register social media settings\n     */\n    registerSocialMediaSettings() {\n        // Kick settings\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.SocialKickUrl`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.SocialKickUrl'),\n            help: app.translator.trans('wusong8899-client1.admin.SocialKickUrlHelp'),\n        });\n\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.SocialKickIcon`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.SocialKickIcon'),\n            help: app.translator.trans('wusong8899-client1.admin.SocialIconHelp'),\n        });\n\n        // Facebook settings\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.SocialFacebookUrl`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.SocialFacebookUrl'),\n            help: app.translator.trans('wusong8899-client1.admin.SocialFacebookUrlHelp'),\n        });\n\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.SocialFacebookIcon`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.SocialFacebookIcon'),\n            help: app.translator.trans('wusong8899-client1.admin.SocialIconHelp'),\n        });\n\n        // Twitter settings\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.SocialTwitterUrl`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.SocialTwitterUrl'),\n            help: app.translator.trans('wusong8899-client1.admin.SocialTwitterUrlHelp'),\n        });\n\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.SocialTwitterIcon`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.SocialTwitterIcon'),\n            help: app.translator.trans('wusong8899-client1.admin.SocialIconHelp'),\n        });\n\n        // YouTube settings\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.SocialYouTubeUrl`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.SocialYouTubeUrl'),\n            help: app.translator.trans('wusong8899-client1.admin.SocialYouTubeUrlHelp'),\n        });\n\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.SocialYouTubeIcon`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.SocialYouTubeIcon'),\n            help: app.translator.trans('wusong8899-client1.admin.SocialIconHelp'),\n        });\n\n        // Instagram settings\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.SocialInstagramUrl`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.SocialInstagramUrl'),\n            help: app.translator.trans('wusong8899-client1.admin.SocialInstagramUrlHelp'),\n        });\n\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.SocialInstagramIcon`,\n            type: 'url',\n            label: app.translator.trans('wusong8899-client1.admin.SocialInstagramIcon'),\n            help: app.translator.trans('wusong8899-client1.admin.SocialIconHelp'),\n        });\n\n        return this;\n    }\n\n    /**\n     * Register dynamic slide settings component\n     * @param {number} maxSlides - Maximum number of slides to configure\n     */\n    registerSlideSettings(maxSlides = 30) {\n        this.extensionData.registerSetting(() => {\n            return m(DynamicSlideSettingsComponent, {\n                extensionId: this.extensionId,\n                maxSlides: maxSlides\n            });\n        });\n        return this;\n    }\n\n    /**\n     * Register all settings for the extension\n     * @param {number} maxSlides - Maximum number of slides to configure\n     */\n    registerAllSettings(maxSlides = 30) {\n        return this\n            .registerTransitionTimeSetting()\n            .registerHeaderIconUrlSetting()\n            .registerSocialMediaSettings()\n            .registerSlideSettings(maxSlides);\n    }\n}\n\n/**\n * Configuration constants\n */\n// Centralized config is in js/src/common/config.\n// Kept for backward compatibility; prefer importing from '../../common/config'.\nexport const EXTENSION_CONFIG = {\n    EXTENSION_ID: 'wusong8899-client1-header-adv',\n    MAX_SLIDES: 30,\n    DEFAULT_TRANSITION_TIME: 5000,\n};\n\n/**\n * Initialize admin settings\n * @param {string} extensionId - The extension identifier\n * @param {number} maxSlides - Maximum number of slides\n */\nexport function initializeAdminSettings(\n    extensionId = EXTENSION_CONFIG.EXTENSION_ID, \n    maxSlides = EXTENSION_CONFIG.MAX_SLIDES\n) {\n    const generator = new SettingsGenerator(extensionId);\n    generator.registerAllSettings(maxSlides);\n}\n", "import app from 'flarum/admin/app';\r\nimport { initializeAdminSettings } from './SettingsGenerator';\r\n\r\napp.initializers.add('wusong8899/client1-header-adv', () => {\r\n    initializeAdminSettings();\r\n});\r\n"], "names": ["DynamicSlideSettingsComponent", "Component", "vnode", "extensionId", "maxSlides", "slides", "i", "linkKey", "image<PERSON>ey", "link", "app", "image", "newSlide", "slideId", "slideIndex", "slide", "field", "value", "s", "<PERSON><PERSON><PERSON>", "key", "timeout<PERSON><PERSON>", "index", "<PERSON><PERSON>", "e", "target", "SettingsGenerator", "EXTENSION_CONFIG", "initializeAdminSettings"], "mappings": "8BAkBA,MAAqBA,UAAsCC,CAA8C,CAAzG,aAAA,CAAA,MAAA,GAAA,SAAA,EACE,KAAQ,OAAsB,CAAA,EAC9B,KAAQ,QAAU,GAClB,KAAQ,OAAS,CAAA,CAEjB,OAAOC,EAAY,CACjB,MAAM,OAAOA,CAAK,EAClB,KAAK,mBAAA,CACP,CAKQ,oBAAqB,CAC3B,KAAM,CAAE,YAAAC,EAAa,UAAAC,EAAY,EAAA,EAAO,KAAK,MACvCC,EAAsB,CAAA,EAG5B,QAASC,EAAI,EAAGA,GAAKF,EAAWE,IAAK,CACnC,MAAMC,EAAU,GAAGJ,CAAW,QAAQG,CAAC,GACjCE,EAAW,GAAGL,CAAW,SAASG,CAAC,GACnCG,EAAOC,EAAI,KAAK,SAASH,CAAO,GAAK,GACrCI,EAAQD,EAAI,KAAK,SAASF,CAAQ,GAAK,IAGzCC,GAAQE,KACVN,EAAO,KAAK,CACV,GAAIC,EACJ,KAAAG,EACA,MAAAE,CAAA,CACD,EACD,KAAK,OAAS,KAAK,IAAI,KAAK,OAAQL,EAAI,CAAC,EAE7C,CAEA,KAAK,OAASD,EAGVA,EAAO,SAAW,GACpB,KAAK,SAAA,CAET,CAKQ,UAAW,CACjB,MAAMO,EAAsB,CAC1B,GAAI,KAAK,SACT,KAAM,GACN,MAAO,EAAA,EAGT,KAAK,OAAO,KAAKA,CAAQ,EACzB,EAAE,OAAA,CACJ,CAKQ,YAAYC,EAAiB,CACnC,KAAM,CAAE,YAAAV,GAAgB,KAAK,MACvBW,EAAa,KAAK,OAAO,UAAUC,GAASA,EAAM,KAAOF,CAAO,EAEtE,GAAIC,IAAe,GAAI,OAEvB,MAAMC,EAAQ,KAAK,OAAOD,CAAU,EAGpC,KAAK,YAAY,GAAGX,CAAW,QAAQY,EAAM,EAAE,GAAI,EAAE,EACrD,KAAK,YAAY,GAAGZ,CAAW,SAASY,EAAM,EAAE,GAAI,EAAE,EAGtD,KAAK,OAAO,OAAOD,EAAY,CAAC,EAG5B,KAAK,OAAO,SAAW,GACzB,KAAK,SAAA,EAGP,EAAE,OAAA,CACJ,CAKQ,YAAYD,EAAiBG,EAAyBC,EAAe,CAC3E,KAAM,CAAE,YAAAd,GAAgB,KAAK,MACvBY,EAAQ,KAAK,OAAO,KAAKG,GAAKA,EAAE,KAAOL,CAAO,EAEpD,GAAI,CAACE,EAAO,OAEZA,EAAMC,CAAK,EAAIC,EAGf,MAAME,EAAaH,IAAU,OACzB,GAAGb,CAAW,QAAQY,EAAM,EAAE,GAC9B,GAAGZ,CAAW,SAASY,EAAM,EAAE,GAEnC,KAAK,YAAYI,EAAYF,CAAK,CACpC,CAKQ,YAAYG,EAAaH,EAAe,CAE9C,MAAMI,EAAa,eAAeD,CAAG,GACrC,aAAc,KAAaC,CAAU,CAAC,EAGrC,KAAaA,CAAU,EAAI,WAAW,IAAM,CAC3CX,EAAI,KAAK,SAASU,CAAG,EAAIH,EAEzBP,EAAI,QAAQ,CACV,OAAQ,OACR,IAAKA,EAAI,MAAM,UAAU,QAAQ,EAAI,YACrC,KAAM,CACJ,CAACU,CAAG,EAAGH,CAAA,CACT,CACD,EAAE,MAAM,IAAM,CAEf,CAAC,CACH,EAAG,GAAG,CACR,CAEA,MAAO,CACL,OAAO,EAAE,iBAAkB,CACzB,EAAE,kBACAP,EAAI,WAAW,MAAM,wCAAwC,CAAA,EAE/D,EAAE,eACAA,EAAI,WAAW,MAAM,4CAA4C,CAAA,EAGnE,EAAE,2BAA4B,CAE5B,KAAK,OAAO,IAAI,CAACK,EAAOO,IAAU,KAAK,YAAYP,EAAOO,CAAK,CAAC,EAGhE,EAAE,qCAAsC,CACtC,EAAEC,EAAQ,CACR,UAAW,yBACX,KAAM,cACN,QAAS,IAAM,KAAK,SAAA,CAAS,EAC5Bb,EAAI,WAAW,MAAM,mCAAmC,CAAC,CAAA,CAC7D,CAAA,CACF,CAAA,CACF,CACH,CAKQ,YAAYK,EAAkBO,EAAe,CACnD,OAAO,EAAE,iCAAkC,CACzC,IAAKP,EAAM,EAAA,EACV,CACD,EAAE,uCAAwC,CACxC,EAAE,KAAML,EAAI,WAAW,MAAM,uCAAwC,CAAE,OAAQY,EAAQ,CAAA,CAAG,CAAC,EAC3F,EAAEC,EAAQ,CACR,UAAW,wBACX,KAAM,eACN,QAAS,IAAM,KAAK,YAAYR,EAAM,EAAE,EACxC,SAAU,KAAK,OAAO,SAAW,CAAA,EAChCL,EAAI,WAAW,MAAM,sCAAsC,CAAC,CAAA,CAChE,EAED,EAAE,uCAAwC,CAExC,EAAE,iBAAkB,CAClB,EAAE,kBACAA,EAAI,WAAW,MAAM,oCAAoC,CAAA,EAE3D,EAAE,oBAAqB,CACrB,KAAM,MACN,YAAa,sBACb,MAAOK,EAAM,KACb,QAAUS,GAAa,CACrB,MAAMC,EAASD,EAAE,OACjB,KAAK,YAAYT,EAAM,GAAI,OAAQU,EAAO,KAAK,CACjD,CAAA,CACD,CAAA,CACF,EAGD,EAAE,iBAAkB,CAClB,EAAE,kBACAf,EAAI,WAAW,MAAM,qCAAqC,CAAA,EAE5D,EAAE,oBAAqB,CACrB,KAAM,MACN,YAAa,gCACb,MAAOK,EAAM,MACb,QAAUS,GAAa,CACrB,MAAMC,EAASD,EAAE,OACjB,KAAK,YAAYT,EAAM,GAAI,QAASU,EAAO,KAAK,CAClD,CAAA,CACD,CAAA,CACF,CAAA,CACF,CAAA,CACF,CACH,CACF,CCvNO,MAAMC,CAAkB,CAC3B,YAAYvB,EAAa,CACrB,KAAK,YAAcA,EACnB,KAAK,cAAgBO,EAAI,cAAc,IAAIP,CAAW,CAC1D,CAKA,+BAAgC,CAC5B,YAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,kBAC5B,KAAM,SACN,MAAOO,EAAI,WAAW,MAAM,yCAAyC,CACjF,CAAS,EACM,IACX,CAKA,8BAA+B,CAC3B,YAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,iBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,wCAAwC,EACpE,KAAMA,EAAI,WAAW,MAAM,4CAA4C,CACnF,CAAS,EACM,IACX,CAKA,6BAA8B,CAE1B,YAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,iBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,wCAAwC,EACpE,KAAMA,EAAI,WAAW,MAAM,4CAA4C,CACnF,CAAS,EAED,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,kBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,yCAAyC,EACrE,KAAMA,EAAI,WAAW,MAAM,yCAAyC,CAChF,CAAS,EAGD,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,qBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,4CAA4C,EACxE,KAAMA,EAAI,WAAW,MAAM,gDAAgD,CACvF,CAAS,EAED,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,sBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,6CAA6C,EACzE,KAAMA,EAAI,WAAW,MAAM,yCAAyC,CAChF,CAAS,EAGD,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,oBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,2CAA2C,EACvE,KAAMA,EAAI,WAAW,MAAM,+CAA+C,CACtF,CAAS,EAED,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,qBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,4CAA4C,EACxE,KAAMA,EAAI,WAAW,MAAM,yCAAyC,CAChF,CAAS,EAGD,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,oBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,2CAA2C,EACvE,KAAMA,EAAI,WAAW,MAAM,+CAA+C,CACtF,CAAS,EAED,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,qBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,4CAA4C,EACxE,KAAMA,EAAI,WAAW,MAAM,yCAAyC,CAChF,CAAS,EAGD,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,sBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,6CAA6C,EACzE,KAAMA,EAAI,WAAW,MAAM,iDAAiD,CACxF,CAAS,EAED,KAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,uBAC5B,KAAM,MACN,MAAOA,EAAI,WAAW,MAAM,8CAA8C,EAC1E,KAAMA,EAAI,WAAW,MAAM,yCAAyC,CAChF,CAAS,EAEM,IACX,CAMA,sBAAsBN,EAAY,GAAI,CAClC,YAAK,cAAc,gBAAgB,IACxB,EAAEJ,EAA+B,CACpC,YAAa,KAAK,YAClB,UAAWI,CAC3B,CAAa,CACJ,EACM,IACX,CAMA,oBAAoBA,EAAY,GAAI,CAChC,OAAO,KACF,8BAA6B,EAC7B,6BAA4B,EAC5B,4BAA2B,EAC3B,sBAAsBA,CAAS,CACxC,CACJ,CAOO,MAAMuB,EAAmB,CAC5B,aAAc,gCACd,WAAY,EAEhB,EAOO,SAASC,EACZzB,EAAcwB,EAAiB,aAC/BvB,EAAYuB,EAAiB,WAC/B,CACoB,IAAID,EAAkBvB,CAAW,EACzC,oBAAoBC,CAAS,CAC3C,CCrKAM,EAAI,aAAa,IAAI,gCAAiC,IAAM,CACxDkB,GACJ,CAAC"}